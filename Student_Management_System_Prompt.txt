===============================================================================
                    STUDENT MANAGEMENT SYSTEM - AI DEVELOPMENT PROMPT
                         نظام إدارة الطلاب - بروومبت للذكاء الاصطناعي
===============================================================================

🎯 PROJECT OVERVIEW / نظرة عامة على المشروع:
============================================

Create a comprehensive Student Management System using C programming language with SQLite database integration. The system should provide a bilingual interface (Arabic/English) and include all CRUD operations with advanced features.

أنشئ نظام إدارة طلاب شامل باستخدام لغة البرمجة C مع تكامل قاعدة بيانات SQLite. يجب أن يوفر النظام واجهة ثنائية اللغة (عربي/إنجليزي) ويتضمن جميع عمليات CRUD مع مميزات متقدمة.

📋 CORE REQUIREMENTS / المتطلبات الأساسية:
==========================================

1. PROGRAMMING LANGUAGE / لغة البرمجة:
   - Use C programming language (C99 standard)
   - استخدم لغة البرمجة C (معيار C99)

2. DATABASE / قاعدة البيانات:
   - SQLite database integration
   - تكامل قاعدة بيانات SQLite
   - Use SQLite amalgamation (sqlite3.c + sqlite3.h)
   - استخدم SQLite amalgamation

3. INTERFACE / الواجهة:
   - Bilingual interface (Arabic/English)
   - واجهة ثنائية اللغة (عربي/إنجليزي)
   - Console-based menu system
   - نظام قوائم يعتمد على وحدة التحكم

🗄️ DATABASE SCHEMA / مخطط قاعدة البيانات:
=========================================

CREATE TABLE students (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    age INTEGER NOT NULL,
    grade REAL NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

🔧 REQUIRED FUNCTIONS / الوظائف المطلوبة:
=======================================

1. DATABASE MANAGEMENT / إدارة قاعدة البيانات:
   - init_database() - Initialize database connection
   - create_table() - Create students table
   - cleanup_database() - Close database connection

2. STUDENT OPERATIONS / عمليات الطلاب:
   - add_student() - Add new student
   - display_all_students() - Show all students in formatted table
   - search_student() - Search by ID or name
   - update_student() - Update student information
   - delete_student() - Delete single student
   - delete_all_students() - Delete all students (with confirmation)

3. VALIDATION FUNCTIONS / وظائف التحقق:
   - validate_student_id() - Check unique student ID
   - validate_email() - Validate email format
   - validate_phone() - Validate phone number
   - validate_age() - Check age range (16-100)
   - validate_grade() - Check grade range (0-100)

4. UTILITY FUNCTIONS / الوظائف المساعدة:
   - get_student_count() - Count total students
   - calculate_average_grade() - Calculate average grade
   - display_statistics() - Show system statistics
   - get_input() - Safe input handling

5. BACKUP FUNCTIONS / وظائف النسخ الاحتياطي:
   - backup_database() - Create database backup
   - restore_database() - Restore from backup

6. TEST DATA FUNCTIONS / وظائف البيانات التجريبية:
   - generate_test_data() - Create 200 test students
   - clear_test_data() - Remove all test data

🛡️ SECURITY REQUIREMENTS / متطلبات الأمان:
==========================================

1. USE PREPARED STATEMENTS / استخدم العبارات المحضرة:
   - Prevent SQL injection attacks
   - منع هجمات حقن SQL
   - Use sqlite3_prepare_v2() and sqlite3_bind_*()

2. INPUT VALIDATION / التحقق من المدخلات:
   - Validate all user inputs
   - تحقق من جميع مدخلات المستخدم
   - Sanitize data before database operations
   - نظف البيانات قبل عمليات قاعدة البيانات

3. ERROR HANDLING / معالجة الأخطاء:
   - Check all SQLite return codes
   - تحقق من جميع رموز الإرجاع لـ SQLite
   - Display meaningful error messages
   - اعرض رسائل خطأ واضحة

📱 USER INTERFACE REQUIREMENTS / متطلبات واجهة المستخدم:
======================================================

1. MAIN MENU / القائمة الرئيسية:
   Display options in both Arabic and English:
   اعرض الخيارات بالعربية والإنجليزية:

   ========================================
   Student Management System / نظام إدارة الطلاب
   ========================================
   1. Add Student / إضافة طالب
   2. Display All Students / عرض جميع الطلاب
   3. Search Student / البحث عن طالب
   4. Update Student / تحديث بيانات طالب
   5. Delete Student / حذف طالب
   6. Statistics / الإحصائيات
   7. Backup Database / نسخ احتياطي لقاعدة البيانات
   8. Restore Database / استعادة قاعدة البيانات
   9. Generate Test Data (200 students) / إنشاء بيانات تجريبية
   10. Delete All Students / حذف جميع الطلاب
   11. Exit / خروج
   ========================================

2. TABLE DISPLAY / عرض الجدول:
   Format student data in a clean table with headers
   نسق بيانات الطلاب في جدول نظيف مع عناوين

3. INPUT PROMPTS / مطالبات الإدخال:
   All prompts should be bilingual
   جميع المطالبات يجب أن تكون ثنائية اللغة

🔨 COMPILATION REQUIREMENTS / متطلبات التجميع:
=============================================

1. COMPILER FLAGS / أعلام المترجم:
   gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management.exe -lm

2. MAKEFILE / ملف البناء:
   Create a Makefile with targets: all, clean, install
   أنشئ Makefile مع الأهداف: all, clean, install

3. SETUP SCRIPTS / سكريبتات الإعداد:
   - setup.bat for Windows
   - setup.sh for Linux/Mac

📊 ADVANCED FEATURES / المميزات المتقدمة:
========================================

1. STATISTICS / الإحصائيات:
   - Total number of students / العدد الإجمالي للطلاب
   - Average grade / متوسط الدرجات
   - Highest and lowest grades / أعلى وأقل الدرجات
   - Age distribution / توزيع الأعمار

2. SEARCH FUNCTIONALITY / وظيفة البحث:
   - Search by student ID / البحث بالرقم الجامعي
   - Search by name (partial matching) / البحث بالاسم (مطابقة جزئية)
   - Case-insensitive search / بحث غير حساس لحالة الأحرف

3. DATA EXPORT / تصدير البيانات:
   - Export to CSV format / تصدير إلى تنسيق CSV
   - Generate reports / إنشاء التقارير

4. BACKUP SYSTEM / نظام النسخ الاحتياطي:
   - Automatic timestamped backups / نسخ احتياطية تلقائية بالتاريخ والوقت
   - Backup verification / التحقق من النسخة الاحتياطية

🧪 TEST DATA SPECIFICATIONS / مواصفات البيانات التجريبية:
========================================================

When generating 200 test students, include:
عند إنشاء 200 طالب تجريبي، اشمل:

- Diverse Arabic and English names / أسماء عربية وإنجليزية متنوعة
- Realistic student IDs (format: STU001-STU200) / أرقام جامعية واقعية
- Ages between 18-25 / أعمار بين 18-25
- Grades between 60-100 / درجات بين 60-100
- Valid email addresses / عناوين بريد إلكتروني صحيحة
- Phone numbers in various formats / أرقام هواتف بتنسيقات مختلفة
- Realistic addresses / عناوين واقعية

💡 IMPLEMENTATION TIPS / نصائح التنفيذ:
====================================

1. MEMORY MANAGEMENT / إدارة الذاكرة:
   - Always finalize prepared statements
   - دائماً أنه العبارات المحضرة
   - Free allocated memory
   - حرر الذاكرة المخصصة

2. ERROR HANDLING / معالجة الأخطاء:
   - Check every SQLite function return value
   - تحقق من قيمة الإرجاع لكل وظيفة SQLite
   - Provide user-friendly error messages
   - وفر رسائل خطأ سهلة الفهم

3. CODE ORGANIZATION / تنظيم الكود:
   - Use meaningful function names
   - استخدم أسماء وظائف واضحة المعنى
   - Add comprehensive comments
   - أضف تعليقات شاملة
   - Separate concerns into different functions
   - افصل الاهتمامات في وظائف مختلفة

📁 DELIVERABLES / المخرجات المطلوبة:
===================================

1. SOURCE FILES / الملفات المصدرية:
   - student_management.c (main source file)
   - sqlite3.c and sqlite3.h (SQLite amalgamation)
   - Makefile
   - setup.bat and setup.sh

2. DOCUMENTATION / التوثيق:
   - README.md with installation and usage instructions
   - USAGE_GUIDE.md with detailed feature explanations
   - Code comments in both Arabic and English

3. EXECUTABLE / الملف التنفيذي:
   - Compiled executable (student_management.exe)
   - Tested on Windows and Linux

🎯 SUCCESS CRITERIA / معايير النجاح:
===================================

The system should:
يجب أن يكون النظام:

✅ Compile without warnings / يُجمع بدون تحذيرات
✅ Handle all CRUD operations correctly / يتعامل مع جميع عمليات CRUD بشكل صحيح
✅ Validate all inputs properly / يتحقق من جميع المدخلات بشكل صحيح
✅ Display bilingual interface / يعرض واجهة ثنائية اللغة
✅ Create and manage SQLite database / ينشئ ويدير قاعدة بيانات SQLite
✅ Generate 200 test students successfully / ينشئ 200 طالب تجريبي بنجاح
✅ Provide backup and restore functionality / يوفر وظائف النسخ الاحتياطي والاستعادة
✅ Handle errors gracefully / يتعامل مع الأخطاء بلطف
✅ Maintain data integrity / يحافظ على سلامة البيانات
✅ Perform efficiently with large datasets / يعمل بكفاءة مع مجموعات البيانات الكبيرة

🔍 DETAILED CODE EXAMPLES / أمثلة كود مفصلة:
============================================

1. DATABASE INITIALIZATION EXAMPLE / مثال تهيئة قاعدة البيانات:

```c
int init_database() {
    int rc = sqlite3_open("students.db", &db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot open database: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return 0;
    }

    // Enable foreign keys
    sqlite3_exec(db, "PRAGMA foreign_keys = ON;", 0, 0, 0);

    return create_table();
}

int create_table() {
    const char *sql = "CREATE TABLE IF NOT EXISTS students ("
                     "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                     "student_id TEXT UNIQUE NOT NULL,"
                     "name TEXT NOT NULL,"
                     "age INTEGER NOT NULL CHECK(age >= 16 AND age <= 100),"
                     "grade REAL NOT NULL CHECK(grade >= 0 AND grade <= 100),"
                     "email TEXT,"
                     "phone TEXT,"
                     "address TEXT,"
                     "created_at DATETIME DEFAULT CURRENT_TIMESTAMP);";

    char *err_msg = 0;
    int rc = sqlite3_exec(db, sql, 0, 0, &err_msg);

    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL error: %s\n", err_msg);
        sqlite3_free(err_msg);
        return 0;
    }

    // Create indexes for better performance
    sqlite3_exec(db, "CREATE INDEX IF NOT EXISTS idx_student_id ON students(student_id);", 0, 0, 0);
    sqlite3_exec(db, "CREATE INDEX IF NOT EXISTS idx_name ON students(name);", 0, 0, 0);

    return 1;
}
```

2. STUDENT STRUCTURE DEFINITION / تعريف هيكل الطالب:

```c
typedef struct {
    int id;
    char student_id[20];
    char name[100];
    int age;
    double grade;
    char email[100];
    char phone[20];
    char address[200];
    char created_at[30];
} Student;
```

3. ADD STUDENT FUNCTION EXAMPLE / مثال وظيفة إضافة طالب:

```c
int add_student() {
    Student student;

    printf("=== Add New Student / إضافة طالب جديد ===\n");

    // Get student ID
    printf("Enter Student ID / أدخل الرقم الجامعي: ");
    if (!get_input(student.student_id, sizeof(student.student_id))) {
        return 0;
    }

    // Validate student ID uniqueness
    if (!validate_student_id(student.student_id)) {
        printf("Error: Student ID already exists! / خطأ: الرقم الجامعي موجود مسبقاً!\n");
        return 0;
    }

    // Get other student information...
    // [Additional input collection code]

    // Prepare SQL statement
    const char *sql = "INSERT INTO students (student_id, name, age, grade, email, phone, address) VALUES (?, ?, ?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        printf("Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 0;
    }

    // Bind parameters
    sqlite3_bind_text(stmt, 1, student.student_id, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, student.name, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 3, student.age);
    sqlite3_bind_double(stmt, 4, student.grade);
    sqlite3_bind_text(stmt, 5, student.email, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 6, student.phone, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 7, student.address, -1, SQLITE_STATIC);

    // Execute statement
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);

    if (rc == SQLITE_DONE) {
        printf("✅ Student added successfully! / تم إضافة الطالب بنجاح!\n");
        return 1;
    } else {
        printf("❌ Failed to add student: %s\n", sqlite3_errmsg(db));
        return 0;
    }
}
```

4. VALIDATION FUNCTIONS EXAMPLES / أمثلة وظائف التحقق:

```c
int validate_email(const char *email) {
    if (strlen(email) == 0) return 1; // Email is optional

    // Simple email validation
    const char *at = strchr(email, '@');
    const char *dot = strrchr(email, '.');

    return (at != NULL && dot != NULL && at < dot &&
            at > email && dot < email + strlen(email) - 1);
}

int validate_student_id(const char *student_id) {
    const char *sql = "SELECT COUNT(*) FROM students WHERE student_id = ?";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return 0;

    sqlite3_bind_text(stmt, 1, student_id, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    int count = 0;
    if (rc == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    sqlite3_finalize(stmt);
    return (count == 0); // Return 1 if ID is unique
}
```

🎨 USER INTERFACE STYLING / تنسيق واجهة المستخدم:
===============================================

```c
void display_header() {
    printf("\n");
    printf("╔══════════════════════════════════════════════════════════════╗\n");
    printf("║           Student Management System / نظام إدارة الطلاب           ║\n");
    printf("╚══════════════════════════════════════════════════════════════╝\n");
}

void display_table_header() {
    printf("\n");
    printf("┌─────┬──────────┬─────────────────────┬─────┬───────┬─────────────────────┬──────────────┐\n");
    printf("│ ID  │ Student  │        Name         │ Age │ Grade │       Email         │    Phone     │\n");
    printf("│     │    ID    │        الاسم        │العمر│الدرجة │   البريد الإلكتروني   │   الهاتف     │\n");
    printf("├─────┼──────────┼─────────────────────┼─────┼───────┼─────────────────────┼──────────────┤\n");
}

void display_student_row(sqlite3_stmt *stmt) {
    printf("│%4d │%-10s│%-21s│%4d │%6.2f │%-21s│%-14s│\n",
           sqlite3_column_int(stmt, 0),    // id
           sqlite3_column_text(stmt, 1),   // student_id
           sqlite3_column_text(stmt, 2),   // name
           sqlite3_column_int(stmt, 3),    // age
           sqlite3_column_double(stmt, 4), // grade
           sqlite3_column_text(stmt, 5),   // email
           sqlite3_column_text(stmt, 6));  // phone
}
```

📊 PERFORMANCE OPTIMIZATION TIPS / نصائح تحسين الأداء:
====================================================

1. USE TRANSACTIONS FOR BULK OPERATIONS / استخدم المعاملات للعمليات المجمعة:

```c
int generate_test_data() {
    sqlite3_exec(db, "BEGIN TRANSACTION;", 0, 0, 0);

    for (int i = 1; i <= 200; i++) {
        // Insert student data
        // [Insert code here]
    }

    sqlite3_exec(db, "COMMIT;", 0, 0, 0);
}
```

2. PREPARE STATEMENTS ONCE, USE MULTIPLE TIMES / حضر العبارات مرة واحدة، استخدمها عدة مرات:

```c
sqlite3_stmt *insert_stmt = NULL;

int prepare_insert_statement() {
    const char *sql = "INSERT INTO students (student_id, name, age, grade, email, phone, address) VALUES (?, ?, ?, ?, ?, ?, ?)";
    return sqlite3_prepare_v2(db, sql, -1, &insert_stmt, NULL);
}
```

🔧 MAKEFILE TEMPLATE / قالب Makefile:
====================================

```makefile
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2
LDFLAGS = -lm
TARGET = student_management
SOURCES = student_management.c sqlite3.c

all: $(TARGET)

$(TARGET): $(SOURCES)
	$(CC) $(CFLAGS) $(SOURCES) -o $(TARGET) $(LDFLAGS)

clean:
	rm -f $(TARGET) $(TARGET).exe *.o

install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

.PHONY: all clean install
```

🚀 SETUP SCRIPT EXAMPLES / أمثلة سكريبتات الإعداد:

setup.bat (Windows):
```batch
@echo off
echo Setting up Student Management System...
echo Downloading SQLite amalgamation...
powershell -Command "Invoke-WebRequest -Uri 'https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip' -OutFile 'sqlite.zip'"
powershell -Command "Expand-Archive -Path 'sqlite.zip' -DestinationPath '.'"
copy sqlite-amalgamation-3460000\sqlite3.c .
copy sqlite-amalgamation-3460000\sqlite3.h .
echo Compiling...
gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management.exe -lm
echo Setup complete!
```

setup.sh (Linux/Mac):
```bash
#!/bin/bash
echo "Setting up Student Management System..."
echo "Downloading SQLite amalgamation..."
wget https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip
unzip sqlite-amalgamation-3460000.zip
cp sqlite-amalgamation-3460000/sqlite3.c .
cp sqlite-amalgamation-3460000/sqlite3.h .
echo "Compiling..."
gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management -lm
echo "Setup complete!"
chmod +x student_management
```

===============================================================================
                    COMPLETE DEVELOPMENT PROMPT
                      بروومبت التطوير الكامل
===============================================================================

This prompt provides everything needed to create a professional Student Management System with SQLite integration. Follow the specifications exactly to ensure compatibility and functionality.

يوفر هذا البروومبت كل ما هو مطلوب لإنشاء نظام إدارة طلاب احترافي مع تكامل SQLite. اتبع المواصفات بدقة لضمان التوافق والوظائف.

===============================================================================
