#!/bin/bash

# Setup script for Student Management System on Linux/Unix
# سكريبت الإعداد لنظام إدارة الطلاب على لينكس/يونكس

echo "========================================"
echo "Student Management System Setup"
echo "نظام إدارة الطلاب - الإعداد"
echo "========================================"
echo

echo "Step 1: Downloading SQLite Amalgamation..."
echo "الخطوة 1: تحميل SQLite Amalgamation..."
echo

# Download SQLite amalgamation
if command -v curl >/dev/null 2>&1; then
    curl -o sqlite-amalgamation.zip https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip
    if [ $? -eq 0 ]; then
        echo "Download completed successfully!"
        echo "تم التحميل بنجاح!"
    else
        echo "Download failed with curl. Trying wget..."
        echo "فشل التحميل مع curl. جاري المحاولة مع wget..."
        
        if command -v wget >/dev/null 2>&1; then
            wget -O sqlite-amalgamation.zip https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip
            if [ $? -ne 0 ]; then
                echo "Download failed. Please download manually."
                echo "فشل التحميل. يرجى التحميل يدوياً."
                exit 1
            fi
        else
            echo "Neither curl nor wget found. Please install one of them."
            echo "لم يتم العثور على curl أو wget. يرجى تثبيت أحدهما."
            exit 1
        fi
    fi
elif command -v wget >/dev/null 2>&1; then
    wget -O sqlite-amalgamation.zip https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip
    if [ $? -ne 0 ]; then
        echo "Download failed. Please download manually."
        echo "فشل التحميل. يرجى التحميل يدوياً."
        exit 1
    fi
else
    echo "Neither curl nor wget found. Please install one of them."
    echo "لم يتم العثور على curl أو wget. يرجى تثبيت أحدهما."
    exit 1
fi

echo
echo "Step 2: Extracting SQLite files..."
echo "الخطوة 2: استخراج ملفات SQLite..."
echo

# Extract the zip file
if command -v unzip >/dev/null 2>&1; then
    unzip -q sqlite-amalgamation.zip
    if [ $? -eq 0 ]; then
        echo "Extraction completed successfully!"
        echo "تم الاستخراج بنجاح!"
        
        # Copy SQLite files
        cp sqlite-amalgamation-*/sqlite3.c .
        cp sqlite-amalgamation-*/sqlite3.h .
        
        # Clean up
        rm -rf sqlite-amalgamation-*
        rm sqlite-amalgamation.zip
        
        echo "SQLite files copied successfully!"
        echo "تم نسخ ملفات SQLite بنجاح!"
    else
        echo "Extraction failed!"
        echo "فشل الاستخراج!"
        exit 1
    fi
else
    echo "unzip command not found. Please install unzip."
    echo "أمر unzip غير موجود. يرجى تثبيت unzip."
    exit 1
fi

echo
echo "Step 3: Checking for C compiler..."
echo "الخطوة 3: فحص مجمع C..."
echo

# Check for GCC
if command -v gcc >/dev/null 2>&1; then
    echo "GCC found: $(gcc --version | head -n1)"
    echo "تم العثور على GCC: $(gcc --version | head -n1)"
    
    echo
    echo "Step 4: Compiling the program..."
    echo "الخطوة 4: تجميع البرنامج..."
    echo
    
    # Compile the program
    gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management -lm -ldl -lpthread
    
    if [ $? -eq 0 ]; then
        echo "Compilation successful!"
        echo "تم التجميع بنجاح!"
        echo
        echo "You can now run the program with: ./student_management"
        echo "يمكنك الآن تشغيل البرنامج بـ: ./student_management"
        
        # Make executable
        chmod +x student_management
        
        echo
        echo "Setup completed successfully!"
        echo "تم الإعداد بنجاح!"
        echo
        echo "Optional: Run 'make install' to install system-wide"
        echo "اختياري: شغل 'make install' للتثبيت على مستوى النظام"
    else
        echo "Compilation failed!"
        echo "فشل التجميع!"
        echo "Please check the error messages above."
        echo "يرجى فحص رسائل الخطأ أعلاه."
        exit 1
    fi
else
    echo "GCC compiler not found!"
    echo "مجمع GCC غير موجود!"
    echo
    echo "Please install GCC:"
    echo "يرجى تثبيت GCC:"
    echo "  Ubuntu/Debian: sudo apt-get install build-essential"
    echo "  CentOS/RHEL:   sudo yum install gcc"
    echo "  Fedora:        sudo dnf install gcc"
    echo "  macOS:         xcode-select --install"
    exit 1
fi

echo
echo "========================================"
echo "Setup completed!"
echo "تم الإعداد!"
echo "========================================"
