@echo off
REM Setup script for Student Management System on Windows
REM سكريبت الإعداد لنظام إدارة الطلاب على ويندوز

echo ========================================
echo Student Management System Setup
echo نظام إدارة الطلاب - الإعداد
echo ========================================
echo.

echo Step 1: Downloading SQLite Amalgamation...
echo الخطوة 1: تحميل SQLite Amalgamation...
echo.

REM Try to download SQLite using PowerShell
powershell -Command "try { Invoke-WebRequest -Uri 'https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip' -OutFile 'sqlite-amalgamation.zip'; Write-Host 'Download completed successfully!' } catch { Write-Host 'Download failed. Please download manually from https://www.sqlite.org/download.html' }"

echo.
echo Step 2: Extracting SQLite files...
echo الخطوة 2: استخراج ملفات SQLite...
echo.

REM Try to extract using PowerShell
if exist sqlite-amalgamation.zip (
    powershell -Command "Expand-Archive -Path 'sqlite-amalgamation.zip' -DestinationPath '.' -Force"
    
    REM Copy the SQLite files
    for /d %%i in (sqlite-amalgamation-*) do (
        if exist "%%i\sqlite3.c" (
            copy "%%i\sqlite3.c" . >nul
            copy "%%i\sqlite3.h" . >nul
            echo SQLite files copied successfully!
            echo تم نسخ ملفات SQLite بنجاح!
        )
    )
    
    REM Clean up
    rmdir /s /q sqlite-amalgamation-* 2>nul
    del sqlite-amalgamation.zip 2>nul
) else (
    echo Download failed. Please follow manual setup instructions.
    echo فشل التحميل. يرجى اتباع تعليمات الإعداد اليدوي.
    echo.
    echo Manual Setup Instructions:
    echo تعليمات الإعداد اليدوي:
    echo 1. Go to https://www.sqlite.org/download.html
    echo 2. Download "sqlite-amalgamation-3460000.zip"
    echo 3. Extract sqlite3.c and sqlite3.h to this directory
    echo 4. Run: gcc student_management.c sqlite3.c -o student_management.exe
    echo.
    pause
    exit /b 1
)

echo.
echo Step 3: Compiling the program...
echo الخطوة 3: تجميع البرنامج...
echo.

REM Try to compile with GCC
gcc --version >nul 2>&1
if %errorlevel% == 0 (
    gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management.exe
    if %errorlevel% == 0 (
        echo Compilation successful!
        echo تم التجميع بنجاح!
        echo.
        echo You can now run the program with: student_management.exe
        echo يمكنك الآن تشغيل البرنامج بـ: student_management.exe
    ) else (
        echo Compilation failed!
        echo فشل التجميع!
    )
) else (
    echo GCC compiler not found. Please install MinGW or Visual Studio.
    echo مجمع GCC غير موجود. يرجى تثبيت MinGW أو Visual Studio.
    echo.
    echo You can download MinGW from: https://www.mingw-w64.org/
    echo يمكنك تحميل MinGW من: https://www.mingw-w64.org/
)

echo.
echo Setup completed!
echo تم الإعداد!
echo.
pause
