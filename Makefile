# Makefile for Student Management System
# ملف البناء لنظام إدارة الطلاب

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2
TARGET = student_management
SOURCES = student_management.c sqlite3.c
OBJECTS = $(SOURCES:.c=.o)

# Default target
all: $(TARGET)

# Build the main executable
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) -lm -ldl -lpthread

# Compile source files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET)

# Install (copy to system path)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# Uninstall
uninstall:
	rm -f /usr/local/bin/$(TARGET)

# Run the program
run: $(TARGET)
	./$(TARGET)

# Create test database
test: $(TARGET)
	./$(TARGET)

# Debug build
debug: CFLAGS += -g -DDEBUG
debug: $(TARGET)

# Help
help:
	@echo "Available targets:"
	@echo "  all      - Build the program (default)"
	@echo "  clean    - Remove build files"
	@echo "  run      - Build and run the program"
	@echo "  test     - Build and run for testing"
	@echo "  debug    - Build with debug information"
	@echo "  install  - Install to system path"
	@echo "  help     - Show this help message"

.PHONY: all clean install uninstall run test debug help
