# Student Management System - Usage Guide
# نظام إدارة الطلاب - دليل الاستخدام

## Quick Start / البداية السريعة

### Windows
```batch
# Run the setup script
setup.bat

# Or manually:
# Download SQLite from https://www.sqlite.org/download.html
# Extract sqlite3.c and sqlite3.h to this directory
# Compile: gcc student_management.c sqlite3.c -o student_management.exe
# Run: student_management.exe
```

### Linux/Unix/macOS
```bash
# Make setup script executable and run
chmod +x setup.sh
./setup.sh

# Or use Makefile
make

# Run the program
./student_management
```

## Detailed Usage / الاستخدام المفصل

### 1. Adding Students / إضافة الطلاب

When you select option 1 from the main menu:
- Enter student name (minimum 2 characters)
- Enter age (15-100 years)
- Enter department name
- Enter GPA (0.0-4.0)
- Enrollment date is automatically set to current date

Example:
```
Enter student name: <PERSON>
Enter age: 20
Enter department: Computer Science
Enter GPA: 3.75
```

### 2. Viewing Students / عرض الطلاب

Option 2 displays all students in a formatted table showing:
- Student ID / الرقم الجامعي
- Name / الاسم
- Age / العمر
- Department / القسم
- GPA / المعدل التراكمي
- Enrollment Date / تاريخ القيد

### 3. Searching Students / البحث عن الطلاب

Option 3 provides two search methods:
- **Search by ID**: Enter exact student ID number
- **Search by Name**: Enter full or partial name (case-insensitive)

Examples:
```
Search by ID: 1001
Search by Name: Ahmed (finds all students with "Ahmed" in their name)
```

### 4. Updating Students / تحديث الطلاب

Option 4 allows you to modify existing student information:
1. Enter student ID to update
2. System displays current information
3. Enter new values (press Enter to keep current value)
4. Confirm changes

### 5. Deleting Students / حذف الطلاب

Option 5 removes a student from the database:
1. Enter student ID to delete
2. System displays student information
3. Confirm deletion (y/n)
4. Student is permanently removed

### 6. Statistics / الإحصائيات

Option 6 shows comprehensive system statistics:
- Total number of students / إجمالي عدد الطلاب
- Average age / متوسط العمر
- Average GPA / متوسط المعدل التراكمي
- Minimum and Maximum GPA / أقل وأعلى معدل
- Department distribution / توزيع الأقسام

### 7. Database Backup / النسخ الاحتياطي

Option 7 creates a backup of your database:
- Automatically generates filename with timestamp
- Format: `backup_students_YYYYMMDD_HHMMSS.db`
- Backup includes all student data and structure

### 8. Database Restore / استعادة البيانات

Option 8 restores database from a backup file:
1. Enter backup filename
2. System warns about data replacement
3. Confirm restoration (y/n)
4. Database is restored from backup

**Warning**: This replaces ALL current data!

### 9. Generate Test Data / إنشاء بيانات الاختبار

Option 9 creates 200 sample students for testing:
- Random Arabic names / أسماء عربية عشوائية
- Ages between 18-25 / أعمار بين 18-25
- 8 different departments / 8 أقسام مختلفة
- Random GPAs between 2.0-4.0 / معدلات عشوائية
- Random enrollment dates / تواريخ قيد عشوائية

This is useful for:
- Testing system performance / اختبار أداء النظام
- Demonstrating features / عرض المميزات
- Training purposes / أغراض التدريب

### 10. Delete All Students / حذف جميع الطلاب

Option 10 removes ALL students from the database:
1. System shows warning and student count
2. Type "DELETE" to confirm (case-sensitive)
3. All student data is permanently removed
4. Student ID counter is reset

**Warning**: This action cannot be undone!

## Input Validation / التحقق من المدخلات

The system validates all inputs:

### Name Validation / التحقق من الاسم
- Minimum 2 characters
- Cannot be empty
- Supports Arabic and English characters

### Age Validation / التحقق من العمر
- Must be between 15 and 100
- Must be a valid integer
- No negative values allowed

### Department Validation / التحقق من القسم
- Minimum 2 characters
- Cannot be empty
- Free text input

### GPA Validation / التحقق من المعدل
- Must be between 0.0 and 4.0
- Must be a valid decimal number
- Supports up to 2 decimal places

## Error Handling / معالجة الأخطاء

The system handles various error scenarios:

### Database Errors / أخطاء قاعدة البيانات
- Connection failures
- Disk space issues
- Permission problems
- Corrupted database files

### Input Errors / أخطاء المدخلات
- Invalid data types
- Out-of-range values
- Empty inputs
- Special characters

### File Errors / أخطاء الملفات
- Missing backup files
- Permission denied
- Disk full
- Invalid file formats

## Performance Tips / نصائح الأداء

### For Large Datasets / للبيانات الكبيرة
- Use search instead of viewing all students
- Regular database backups
- Monitor disk space
- Consider archiving old data

### Memory Management / إدارة الذاكرة
- System automatically manages memory
- Database connections are properly closed
- No memory leaks in normal operation

## Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

1. **"Database locked" error**
   - Close other instances of the program
   - Check file permissions
   - Restart the program

2. **"Cannot open database" error**
   - Check disk space
   - Verify file permissions
   - Ensure directory is writable

3. **Compilation errors**
   - Ensure SQLite files are present
   - Check compiler installation
   - Verify all dependencies

4. **Character encoding issues**
   - Set proper locale settings
   - Use UTF-8 encoding
   - Check terminal settings

### Getting Help / الحصول على المساعدة

If you encounter issues:
1. Check this usage guide
2. Review error messages carefully
3. Ensure all setup steps were completed
4. Try regenerating test data
5. Create a backup before troubleshooting

## Best Practices / أفضل الممارسات

### Data Management / إدارة البيانات
- Regular backups (daily/weekly)
- Validate data before entry
- Use meaningful department names
- Keep student information up-to-date

### System Maintenance / صيانة النظام
- Monitor database size
- Clean up old backup files
- Update SQLite when needed
- Test restore procedures

### Security / الأمان
- Protect database files
- Regular security updates
- Backup encryption (if needed)
- Access control implementation
