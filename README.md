# Student Management System / نظام إدارة الطلاب

A comprehensive student management system built with C and SQLite, featuring Arabic language support.

نظام شامل لإدارة الطلاب مبني بلغة C وقاعدة بيانات SQLite مع دعم اللغة العربية.

## Features / المميزات

### Core Functionality / الوظائف الأساسية
- ✅ Add new students / إضافة طلاب جدد
- ✅ View all students / عرض جميع الطلاب  
- ✅ Search students by ID or name / البحث عن الطلاب بالرقم أو الاسم
- ✅ Update student information / تحديث معلومات الطلاب
- ✅ Delete students / حذف الطلاب

### Advanced Features / المميزات المتقدمة
- ✅ Statistics and reporting / الإحصائيات والتقارير
- ✅ Database backup and restore / النسخ الاحتياطي واستعادة البيانات
- ✅ Generate 200 test students / إنشاء 200 طالب اختبار
- ✅ Delete all students option / خيار حذف جميع الطلاب
- ✅ Arabic language support / دعم اللغة العربية
- ✅ Input validation and error handling / التحقق من المدخلات ومعالجة الأخطاء

## Database Schema / مخطط قاعدة البيانات

```sql
CREATE TABLE students (
    student_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    age INTEGER NOT NULL CHECK(age > 0 AND age < 150),
    department TEXT,
    gpa REAL CHECK(gpa >= 0.0 AND gpa <= 4.0),
    enrollment_date TEXT DEFAULT CURRENT_DATE
);
```

## Installation / التثبيت

### Prerequisites / المتطلبات المسبقة

1. **C Compiler** (GCC recommended)
2. **SQLite Amalgamation** - Download from [SQLite Official Website](https://www.sqlite.org/download.html)

### Setup Steps / خطوات الإعداد

1. **Download SQLite Amalgamation:**
   ```bash
   # Download the latest SQLite amalgamation
   curl -o sqlite-amalgamation.zip https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip
   
   # Extract the files
   unzip sqlite-amalgamation.zip
   
   # Copy sqlite3.c and sqlite3.h to project directory
   cp sqlite-amalgamation-*/sqlite3.c .
   cp sqlite-amalgamation-*/sqlite3.h .
   ```

2. **Compile the Program:**
   ```bash
   # Using Makefile (recommended)
   make
   
   # Or compile manually
   gcc -Wall -Wextra -std=c99 -O2 student_management.c sqlite3.c -o student_management -lm -ldl -lpthread
   ```

3. **Run the Program:**
   ```bash
   ./student_management
   ```

## Usage / الاستخدام

### Main Menu Options / خيارات القائمة الرئيسية

1. **Add Student / إضافة طالب** - Add a new student to the database
2. **View All Students / عرض جميع الطلاب** - Display all students in a formatted table
3. **Search Student / البحث عن طالب** - Search by ID or name (partial matching supported)
4. **Update Student / تحديث بيانات طالب** - Modify existing student information
5. **Delete Student / حذف طالب** - Remove a student from the database
6. **Statistics / الإحصائيات** - View system statistics and reports
7. **Backup Database / نسخ احتياطي للبيانات** - Create a backup of the database
8. **Restore Database / استعادة البيانات** - Restore from a backup file
9. **Generate Test Data / إنشاء بيانات اختبار** - Add 200 sample students for testing
10. **Delete All Students / حذف جميع الطلاب** - Clear all student data (requires confirmation)
11. **About System / حول النظام** - Display system information

### Example Usage / مثال على الاستخدام

```bash
# Run the program
./student_management

# Follow the interactive menu
# اتبع القائمة التفاعلية

# To generate test data for demonstration:
# لإنشاء بيانات اختبار للعرض:
# Choose option 9 from the main menu
# اختر الخيار 9 من القائمة الرئيسية
```

## File Structure / هيكل الملفات

```
student_management/
├── student_management.c    # Main program file / ملف البرنامج الرئيسي
├── sqlite3.h              # SQLite header file / ملف رأس SQLite
├── sqlite3.c              # SQLite implementation / تنفيذ SQLite
├── Makefile               # Build configuration / إعدادات البناء
├── README.md              # Documentation / التوثيق
└── students.db            # Database file (created automatically) / ملف قاعدة البيانات
```

## Technical Details / التفاصيل التقنية

### Security Features / مميزات الأمان
- **Prepared Statements** - Protection against SQL injection
- **Input Validation** - Comprehensive validation for all user inputs
- **Error Handling** - Robust error handling throughout the system

### Performance Features / مميزات الأداء
- **Efficient Queries** - Optimized SQL queries with proper indexing
- **Memory Management** - Proper cleanup of database resources
- **Batch Operations** - Efficient handling of large datasets

## Testing / الاختبار

### Test Data Generation / إنشاء بيانات الاختبار
The system includes a built-in test data generator that creates 200 sample students with:
- Random Arabic names / أسماء عربية عشوائية
- Ages between 18-25 / أعمار بين 18-25
- Various departments / أقسام متنوعة
- Random GPAs between 2.0-4.0 / معدلات عشوائية بين 2.0-4.0
- Random enrollment dates within the last 4 years / تواريخ قيد عشوائية خلال آخر 4 سنوات

### Manual Testing / الاختبار اليدوي
1. Test all CRUD operations / اختبر جميع العمليات الأساسية
2. Test input validation / اختبر التحقق من المدخلات
3. Test error scenarios / اختبر سيناريوهات الأخطاء
4. Test backup and restore / اختبر النسخ الاحتياطي والاستعادة

## Future Enhancements / التحسينات المستقبلية

- [ ] Web interface / واجهة ويب
- [ ] Course management / إدارة المواد الدراسية
- [ ] Grade tracking / تتبع الدرجات
- [ ] Report generation (PDF/Excel) / إنشاء التقارير
- [ ] Multi-user support / دعم متعدد المستخدمين
- [ ] Data import/export / استيراد وتصدير البيانات
- [ ] Advanced search filters / مرشحات بحث متقدمة
- [ ] Student photos / صور الطلاب

## License / الترخيص

This project is open source and available under the MIT License.

هذا المشروع مفتوح المصدر ومتاح تحت ترخيص MIT.

## Support / الدعم

For questions or issues, please create an issue in the project repository.

للأسئلة أو المشاكل، يرجى إنشاء مشكلة في مستودع المشروع.
