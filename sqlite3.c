/*
** SQLite3 Implementation Stub - For Demonstration Purposes Only
** 
** IMPORTANT: This is a minimal stub implementation for demonstration.
** For actual use, you MUST download the official SQLite amalgamation from:
** https://www.sqlite.org/download.html
**
** The official sqlite3.c file is approximately 8MB and contains the complete
** SQLite database engine implementation.
*/

#include "sqlite3.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/*
** This is a stub implementation that will print error messages
** indicating that the real SQLite library is needed.
*/

static void print_stub_error(const char* function_name) {
    fprintf(stderr, "\n=== SQLITE STUB ERROR ===\n");
    fprintf(stderr, "Function '%s' called, but this is only a stub implementation.\n", function_name);
    fprintf(stderr, "\nTo use this program, you need to:\n");
    fprintf(stderr, "1. Download SQLite amalgamation from: https://www.sqlite.org/download.html\n");
    fprintf(stderr, "2. Extract sqlite3.c and sqlite3.h from the downloaded archive\n");
    fprintf(stderr, "3. Replace this stub file with the real sqlite3.c\n");
    fprintf(stderr, "4. Recompile the program\n");
    fprintf(stderr, "\nExample download command:\n");
    fprintf(stderr, "curl -o sqlite-amalgamation.zip https://www.sqlite.org/2024/sqlite-amalgamation-3460000.zip\n");
    fprintf(stderr, "unzip sqlite-amalgamation.zip\n");
    fprintf(stderr, "cp sqlite-amalgamation-*/sqlite3.c .\n");
    fprintf(stderr, "cp sqlite-amalgamation-*/sqlite3.h .\n");
    fprintf(stderr, "make clean && make\n");
    fprintf(stderr, "========================\n\n");
}

/* Stub implementations */
int sqlite3_open(const char *filename, sqlite3 **ppDb) {
    print_stub_error("sqlite3_open");
    return SQLITE_ERROR;
}

int sqlite3_close(sqlite3 *db) {
    print_stub_error("sqlite3_close");
    return SQLITE_ERROR;
}

const char *sqlite3_errmsg(sqlite3 *db) {
    return "SQLite stub implementation - real library required";
}

int sqlite3_exec(sqlite3 *db, const char *sql, int (*callback)(void*,int,char**,char**), void *arg, char **errmsg) {
    print_stub_error("sqlite3_exec");
    return SQLITE_ERROR;
}

int sqlite3_prepare_v2(sqlite3 *db, const char *zSql, int nByte, sqlite3_stmt **ppStmt, const char **pzTail) {
    print_stub_error("sqlite3_prepare_v2");
    return SQLITE_ERROR;
}

int sqlite3_step(sqlite3_stmt *pStmt) {
    print_stub_error("sqlite3_step");
    return SQLITE_ERROR;
}

int sqlite3_finalize(sqlite3_stmt *pStmt) {
    print_stub_error("sqlite3_finalize");
    return SQLITE_ERROR;
}

int sqlite3_reset(sqlite3_stmt *pStmt) {
    print_stub_error("sqlite3_reset");
    return SQLITE_ERROR;
}

int sqlite3_bind_int(sqlite3_stmt *pStmt, int i, int iValue) {
    print_stub_error("sqlite3_bind_int");
    return SQLITE_ERROR;
}

int sqlite3_bind_double(sqlite3_stmt *pStmt, int i, double rValue) {
    print_stub_error("sqlite3_bind_double");
    return SQLITE_ERROR;
}

int sqlite3_bind_text(sqlite3_stmt *pStmt, int i, const char *zData, int nData, sqlite3_destructor_type xDel) {
    print_stub_error("sqlite3_bind_text");
    return SQLITE_ERROR;
}

int sqlite3_column_int(sqlite3_stmt *pStmt, int iCol) {
    print_stub_error("sqlite3_column_int");
    return 0;
}

double sqlite3_column_double(sqlite3_stmt *pStmt, int iCol) {
    print_stub_error("sqlite3_column_double");
    return 0.0;
}

const unsigned char *sqlite3_column_text(sqlite3_stmt *pStmt, int iCol) {
    print_stub_error("sqlite3_column_text");
    return (const unsigned char*)"";
}

long long sqlite3_last_insert_rowid(sqlite3 *db) {
    print_stub_error("sqlite3_last_insert_rowid");
    return 0;
}

void sqlite3_free(void *p) {
    if (p) free(p);
}

sqlite3_backup *sqlite3_backup_init(sqlite3 *pDest, const char *zDestName, sqlite3 *pSource, const char *zSourceName) {
    print_stub_error("sqlite3_backup_init");
    return NULL;
}

int sqlite3_backup_step(sqlite3_backup *p, int nPage) {
    print_stub_error("sqlite3_backup_step");
    return SQLITE_ERROR;
}

int sqlite3_backup_finish(sqlite3_backup *p) {
    print_stub_error("sqlite3_backup_finish");
    return SQLITE_ERROR;
}
