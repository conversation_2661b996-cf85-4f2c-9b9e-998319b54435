/*
 * Test System for Student Management System
 * نظام اختبار لنظام إدارة الطلاب
 * 
 * This file contains automated tests for the student management system
 * هذا الملف يحتوي على اختبارات آلية لنظام إدارة الطلاب
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include "sqlite3.h"

// Test database name
#define TEST_DB "test_students.db"

// Test functions
void test_database_creation(void);
void test_student_operations(void);
void test_input_validation(void);
void test_statistics(void);
void test_backup_restore(void);
void cleanup_test_db(void);

// Global test database
sqlite3 *test_db = NULL;

int main(void) {
    printf("=== Student Management System Tests ===\n");
    printf("=== اختبارات نظام إدارة الطلاب ===\n\n");
    
    // Initialize test database
    int rc = sqlite3_open(TEST_DB, &test_db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot open test database: %s\n", sqlite3_errmsg(test_db));
        return 1;
    }
    
    // Create tables
    const char *create_sql = 
        "CREATE TABLE IF NOT EXISTS students ("
        "student_id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "name TEXT NOT NULL,"
        "age INTEGER NOT NULL CHECK(age > 0 AND age < 150),"
        "department TEXT,"
        "gpa REAL CHECK(gpa >= 0.0 AND gpa <= 4.0),"
        "enrollment_date TEXT DEFAULT CURRENT_DATE"
        ");";
    
    char *err_msg = 0;
    rc = sqlite3_exec(test_db, create_sql, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL error: %s\n", err_msg);
        sqlite3_free(err_msg);
        cleanup_test_db();
        return 1;
    }
    
    // Run tests
    printf("Running tests...\n");
    printf("جاري تشغيل الاختبارات...\n\n");
    
    test_database_creation();
    test_student_operations();
    test_statistics();
    
    printf("\n=== All Tests Completed ===\n");
    printf("=== تم إنجاز جميع الاختبارات ===\n");
    
    cleanup_test_db();
    return 0;
}

void test_database_creation(void) {
    printf("Test 1: Database Creation / اختبار إنشاء قاعدة البيانات\n");
    
    // Test table existence
    const char *sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='students'";
    sqlite3_stmt *stmt;
    
    int rc = sqlite3_prepare_v2(test_db, sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_ROW);
    
    const char *table_name = (const char*)sqlite3_column_text(stmt, 0);
    assert(strcmp(table_name, "students") == 0);
    
    sqlite3_finalize(stmt);
    printf("✓ Database and table created successfully\n");
    printf("✓ تم إنشاء قاعدة البيانات والجدول بنجاح\n\n");
}

void test_student_operations(void) {
    printf("Test 2: Student CRUD Operations / اختبار عمليات الطلاب الأساسية\n");
    
    // Test INSERT
    const char *insert_sql = "INSERT INTO students (name, age, department, gpa) VALUES (?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    
    int rc = sqlite3_prepare_v2(test_db, insert_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    sqlite3_bind_text(stmt, 1, "Test Student", -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 2, 20);
    sqlite3_bind_text(stmt, 3, "Computer Science", -1, SQLITE_STATIC);
    sqlite3_bind_double(stmt, 4, 3.5);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_DONE);
    
    long long student_id = sqlite3_last_insert_rowid(test_db);
    assert(student_id > 0);
    
    sqlite3_finalize(stmt);
    printf("✓ Student insertion successful (ID: %lld)\n", student_id);
    printf("✓ تم إدراج الطالب بنجاح (الرقم: %lld)\n", student_id);
    
    // Test SELECT
    const char *select_sql = "SELECT name, age, department, gpa FROM students WHERE student_id = ?";
    rc = sqlite3_prepare_v2(test_db, select_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    sqlite3_bind_int64(stmt, 1, student_id);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_ROW);
    
    const char *name = (const char*)sqlite3_column_text(stmt, 0);
    int age = sqlite3_column_int(stmt, 1);
    const char *department = (const char*)sqlite3_column_text(stmt, 2);
    double gpa = sqlite3_column_double(stmt, 3);
    
    assert(strcmp(name, "Test Student") == 0);
    assert(age == 20);
    assert(strcmp(department, "Computer Science") == 0);
    assert(gpa == 3.5);
    
    sqlite3_finalize(stmt);
    printf("✓ Student retrieval successful\n");
    printf("✓ تم استرداد بيانات الطالب بنجاح\n");
    
    // Test UPDATE
    const char *update_sql = "UPDATE students SET gpa = ? WHERE student_id = ?";
    rc = sqlite3_prepare_v2(test_db, update_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    sqlite3_bind_double(stmt, 1, 3.8);
    sqlite3_bind_int64(stmt, 2, student_id);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_DONE);
    
    sqlite3_finalize(stmt);
    printf("✓ Student update successful\n");
    printf("✓ تم تحديث بيانات الطالب بنجاح\n");
    
    // Test DELETE
    const char *delete_sql = "DELETE FROM students WHERE student_id = ?";
    rc = sqlite3_prepare_v2(test_db, delete_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    sqlite3_bind_int64(stmt, 1, student_id);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_DONE);
    
    sqlite3_finalize(stmt);
    printf("✓ Student deletion successful\n");
    printf("✓ تم حذف الطالب بنجاح\n\n");
}

void test_statistics(void) {
    printf("Test 3: Statistics Functions / اختبار دوال الإحصائيات\n");
    
    // Insert test data
    const char *insert_sql = "INSERT INTO students (name, age, department, gpa) VALUES (?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    
    const char *test_students[][4] = {
        {"Ahmed Ali", "20", "Computer Science", "3.5"},
        {"Fatima Hassan", "22", "Engineering", "3.8"},
        {"Omar Mohammed", "21", "Computer Science", "3.2"},
        {"Aisha Ahmed", "19", "Medicine", "3.9"},
        {"Khalid Saad", "23", "Engineering", "3.6"}
    };
    
    int rc = sqlite3_prepare_v2(test_db, insert_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    for (int i = 0; i < 5; i++) {
        sqlite3_bind_text(stmt, 1, test_students[i][0], -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 2, atoi(test_students[i][1]));
        sqlite3_bind_text(stmt, 3, test_students[i][2], -1, SQLITE_STATIC);
        sqlite3_bind_double(stmt, 4, atof(test_students[i][3]));
        
        rc = sqlite3_step(stmt);
        assert(rc == SQLITE_DONE);
        
        sqlite3_reset(stmt);
    }
    
    sqlite3_finalize(stmt);
    
    // Test count
    const char *count_sql = "SELECT COUNT(*) FROM students";
    rc = sqlite3_prepare_v2(test_db, count_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_ROW);
    
    int count = sqlite3_column_int(stmt, 0);
    assert(count == 5);
    
    sqlite3_finalize(stmt);
    printf("✓ Student count: %d\n", count);
    printf("✓ عدد الطلاب: %d\n", count);
    
    // Test average age
    const char *avg_sql = "SELECT AVG(age) FROM students";
    rc = sqlite3_prepare_v2(test_db, avg_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    rc = sqlite3_step(stmt);
    assert(rc == SQLITE_ROW);
    
    double avg_age = sqlite3_column_double(stmt, 0);
    assert(avg_age == 21.0);
    
    sqlite3_finalize(stmt);
    printf("✓ Average age: %.1f\n", avg_age);
    printf("✓ متوسط العمر: %.1f\n", avg_age);
    
    // Test department statistics
    const char *dept_sql = "SELECT department, COUNT(*) FROM students GROUP BY department";
    rc = sqlite3_prepare_v2(test_db, dept_sql, -1, &stmt, NULL);
    assert(rc == SQLITE_OK);
    
    printf("✓ Department statistics:\n");
    printf("✓ إحصائيات الأقسام:\n");
    
    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        const char *dept = (const char*)sqlite3_column_text(stmt, 0);
        int dept_count = sqlite3_column_int(stmt, 1);
        printf("  %s: %d\n", dept, dept_count);
    }
    
    sqlite3_finalize(stmt);
    printf("\n");
}

void cleanup_test_db(void) {
    if (test_db) {
        sqlite3_close(test_db);
        test_db = NULL;
    }
    
    // Remove test database file
    remove(TEST_DB);
    
    printf("Test database cleaned up.\n");
    printf("تم تنظيف قاعدة بيانات الاختبار.\n");
}
