/*
 * Student Management System using SQLite
 * نظام إدارة الطلاب باستخدام SQLite
 * 
 * Features:
 * - Add, view, search, update, delete students
 * - Statistics and reporting
 * - Backup and restore functionality
 * - Test data generation
 * - Arabic language support
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <locale.h>
#include "sqlite3.h"

// Constants
#define MAX_NAME_LENGTH 100
#define MAX_DEPT_LENGTH 50
#define MAX_QUERY_LENGTH 1000
#define MAX_FILENAME_LENGTH 256

// Student structure
typedef struct {
    int student_id;
    char name[MAX_NAME_LENGTH];
    int age;
    char department[MAX_DEPT_LENGTH];
    float gpa;
    char enrollment_date[20];
} Student;

// Global database connection
sqlite3 *db = NULL;

// Function prototypes
int initialize_database(void);
void cleanup_database(void);
int create_tables(void);
void display_main_menu(void);
void handle_menu_choice(int choice);

// Student operations
int add_student(void);
int view_all_students(void);
int search_student(void);
int update_student(void);
int delete_student(void);

// Utility functions
int get_student_count(void);
float get_average_age(void);
void display_statistics(void);
int backup_database(void);
int restore_database(void);
int generate_test_data(void);
int delete_all_students(void);

// Input validation functions
int get_integer_input(const char* prompt, int min, int max);
float get_float_input(const char* prompt, float min, float max);
void get_string_input(const char* prompt, char* buffer, int max_length);
void clear_input_buffer(void);

/*
 * Main function - Entry point of the program
 * الدالة الرئيسية - نقطة دخول البرنامج
 */
int main(void) {
    // Set locale for Arabic support
    setlocale(LC_ALL, "");
    
    printf("=== Student Management System ===\n");
    printf("=== نظام إدارة الطلاب ===\n\n");
    
    // Initialize database
    if (initialize_database() != 0) {
        printf("Error: Failed to initialize database!\n");
        printf("خطأ: فشل في تهيئة قاعدة البيانات!\n");
        return 1;
    }
    
    printf("Database initialized successfully.\n");
    printf("تم تهيئة قاعدة البيانات بنجاح.\n\n");
    
    // Main program loop
    int choice;
    do {
        display_main_menu();
        choice = get_integer_input("Enter your choice / أدخل اختيارك: ", 0, 11);
        handle_menu_choice(choice);
        
        if (choice != 0) {
            printf("\nPress Enter to continue...\n");
            printf("اضغط Enter للمتابعة...\n");
            getchar();
        }
    } while (choice != 0);
    
    cleanup_database();
    printf("Thank you for using Student Management System!\n");
    printf("شكراً لاستخدام نظام إدارة الطلاب!\n");
    
    return 0;
}

/*
 * Initialize database connection and create tables
 * تهيئة اتصال قاعدة البيانات وإنشاء الجداول
 */
int initialize_database(void) {
    int rc = sqlite3_open("students.db", &db);
    
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot open database: %s\n", sqlite3_errmsg(db));
        return 1;
    }
    
    return create_tables();
}

/*
 * Create necessary database tables
 * إنشاء جداول قاعدة البيانات المطلوبة
 */
int create_tables(void) {
    const char *sql = 
        "CREATE TABLE IF NOT EXISTS students ("
        "student_id INTEGER PRIMARY KEY AUTOINCREMENT,"
        "name TEXT NOT NULL,"
        "age INTEGER NOT NULL CHECK(age > 0 AND age < 150),"
        "department TEXT,"
        "gpa REAL CHECK(gpa >= 0.0 AND gpa <= 4.0),"
        "enrollment_date TEXT DEFAULT CURRENT_DATE"
        ");";
    
    char *err_msg = 0;
    int rc = sqlite3_exec(db, sql, 0, 0, &err_msg);
    
    if (rc != SQLITE_OK) {
        fprintf(stderr, "SQL error: %s\n", err_msg);
        sqlite3_free(err_msg);
        return 1;
    }
    
    return 0;
}

/*
 * Clean up database connection
 * تنظيف اتصال قاعدة البيانات
 */
void cleanup_database(void) {
    if (db) {
        sqlite3_close(db);
        db = NULL;
    }
}

/*
 * Display main menu options
 * عرض خيارات القائمة الرئيسية
 */
void display_main_menu(void) {
    printf("\n==================================================\n");
    printf("MAIN MENU / القائمة الرئيسية\n");
    printf("==================================================\n");
    printf("1. Add Student / إضافة طالب\n");
    printf("2. View All Students / عرض جميع الطلاب\n");
    printf("3. Search Student / البحث عن طالب\n");
    printf("4. Update Student / تحديث بيانات طالب\n");
    printf("5. Delete Student / حذف طالب\n");
    printf("6. Statistics / الإحصائيات\n");
    printf("7. Backup Database / نسخ احتياطي للبيانات\n");
    printf("8. Restore Database / استعادة البيانات\n");
    printf("9. Generate Test Data / إنشاء بيانات اختبار\n");
    printf("10. Delete All Students / حذف جميع الطلاب\n");
    printf("11. About System / حول النظام\n");
    printf("0. Exit / خروج\n");
    printf("==================================================\n");
}

/*
 * Handle menu choice selection
 * معالجة اختيار القائمة
 */
void handle_menu_choice(int choice) {
    switch (choice) {
        case 1:
            add_student();
            break;
        case 2:
            view_all_students();
            break;
        case 3:
            search_student();
            break;
        case 4:
            update_student();
            break;
        case 5:
            delete_student();
            break;
        case 6:
            display_statistics();
            break;
        case 7:
            backup_database();
            break;
        case 8:
            restore_database();
            break;
        case 9:
            generate_test_data();
            break;
        case 10:
            delete_all_students();
            break;
        case 11:
            printf("\nStudent Management System v1.0\n");
            printf("نظام إدارة الطلاب الإصدار 1.0\n");
            printf("Developed with SQLite integration\n");
            printf("تم تطويره مع تكامل SQLite\n");
            break;
        case 0:
            printf("Exiting system...\n");
            printf("جاري الخروج من النظام...\n");
            break;
        default:
            printf("Invalid choice! Please try again.\n");
            printf("اختيار غير صحيح! يرجى المحاولة مرة أخرى.\n");
            break;
    }
}

/*
 * Add a new student to the database
 * إضافة طالب جديد إلى قاعدة البيانات
 */
int add_student(void) {
    printf("\n=== Add New Student / إضافة طالب جديد ===\n");

    Student student;

    // Get student information
    get_string_input("Enter student name / أدخل اسم الطالب: ", student.name, MAX_NAME_LENGTH);
    student.age = get_integer_input("Enter age / أدخل العمر: ", 15, 100);
    get_string_input("Enter department / أدخل القسم: ", student.department, MAX_DEPT_LENGTH);
    student.gpa = get_float_input("Enter GPA (0.0-4.0) / أدخل المعدل التراكمي: ", 0.0, 4.0);

    // Get current date
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(student.enrollment_date, sizeof(student.enrollment_date), "%Y-%m-%d", tm_info);

    // Prepare SQL statement
    const char *sql = "INSERT INTO students (name, age, department, gpa, enrollment_date) VALUES (?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    // Bind parameters
    sqlite3_bind_text(stmt, 1, student.name, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 2, student.age);
    sqlite3_bind_text(stmt, 3, student.department, -1, SQLITE_STATIC);
    sqlite3_bind_double(stmt, 4, student.gpa);
    sqlite3_bind_text(stmt, 5, student.enrollment_date, -1, SQLITE_STATIC);

    // Execute statement
    rc = sqlite3_step(stmt);
    if (rc == SQLITE_DONE) {
        printf("Student added successfully! ID: %lld\n", sqlite3_last_insert_rowid(db));
        printf("تم إضافة الطالب بنجاح! الرقم الجامعي: %lld\n", sqlite3_last_insert_rowid(db));
    } else {
        fprintf(stderr, "Failed to add student: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        return 1;
    }

    sqlite3_finalize(stmt);
    return 0;
}

/*
 * View all students in the database
 * عرض جميع الطلاب في قاعدة البيانات
 */
int view_all_students(void) {
    printf("\n=== All Students / جميع الطلاب ===\n");

    const char *sql = "SELECT student_id, name, age, department, gpa, enrollment_date FROM students ORDER BY student_id";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    printf("\n%-5s %-20s %-5s %-15s %-6s %-12s\n", "ID", "Name", "Age", "Department", "GPA", "Enrollment");
    printf("%-5s %-20s %-5s %-15s %-6s %-12s\n", "الرقم", "الاسم", "العمر", "القسم", "المعدل", "تاريخ القيد");
    printf("================================================================\n");

    int count = 0;
    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        printf("%-5d %-20s %-5d %-15s %-6.2f %-12s\n",
               sqlite3_column_int(stmt, 0),    // student_id
               sqlite3_column_text(stmt, 1),   // name
               sqlite3_column_int(stmt, 2),    // age
               sqlite3_column_text(stmt, 3),   // department
               sqlite3_column_double(stmt, 4), // gpa
               sqlite3_column_text(stmt, 5));  // enrollment_date
        count++;
    }

    if (count == 0) {
        printf("No students found in the database.\n");
        printf("لا توجد بيانات طلاب في قاعدة البيانات.\n");
    } else {
        printf("================================================================\n");
        printf("Total students: %d\n", count);
        printf("إجمالي الطلاب: %d\n", count);
    }

    if (rc != SQLITE_DONE) {
        fprintf(stderr, "Error reading data: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        return 1;
    }

    sqlite3_finalize(stmt);
    return 0;
}

/*
 * Search for a student by ID or name
 * البحث عن طالب بالرقم الجامعي أو الاسم
 */
int search_student(void) {
    printf("\n=== Search Student / البحث عن طالب ===\n");
    printf("1. Search by ID / البحث بالرقم الجامعي\n");
    printf("2. Search by Name / البحث بالاسم\n");

    int choice = get_integer_input("Choose search method / اختر طريقة البحث: ", 1, 2);

    sqlite3_stmt *stmt;
    const char *sql;
    int rc;

    if (choice == 1) {
        // Search by ID
        int student_id = get_integer_input("Enter student ID / أدخل الرقم الجامعي: ", 1, 999999);
        sql = "SELECT student_id, name, age, department, gpa, enrollment_date FROM students WHERE student_id = ?";

        rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
        if (rc != SQLITE_OK) {
            fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
            return 1;
        }

        sqlite3_bind_int(stmt, 1, student_id);
    } else {
        // Search by name
        char search_name[MAX_NAME_LENGTH];
        get_string_input("Enter student name (partial match allowed) / أدخل اسم الطالب: ", search_name, MAX_NAME_LENGTH);

        sql = "SELECT student_id, name, age, department, gpa, enrollment_date FROM students WHERE name LIKE ?";

        rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
        if (rc != SQLITE_OK) {
            fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
            return 1;
        }

        char pattern[MAX_NAME_LENGTH + 2];
        snprintf(pattern, sizeof(pattern), "%%%s%%", search_name);
        sqlite3_bind_text(stmt, 1, pattern, -1, SQLITE_STATIC);
    }

    printf("\n%-5s %-20s %-5s %-15s %-6s %-12s\n", "ID", "Name", "Age", "Department", "GPA", "Enrollment");
    printf("%-5s %-20s %-5s %-15s %-6s %-12s\n", "الرقم", "الاسم", "العمر", "القسم", "المعدل", "تاريخ القيد");
    printf("================================================================\n");

    int count = 0;
    while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
        printf("%-5d %-20s %-5d %-15s %-6.2f %-12s\n",
               sqlite3_column_int(stmt, 0),    // student_id
               sqlite3_column_text(stmt, 1),   // name
               sqlite3_column_int(stmt, 2),    // age
               sqlite3_column_text(stmt, 3),   // department
               sqlite3_column_double(stmt, 4), // gpa
               sqlite3_column_text(stmt, 5));  // enrollment_date
        count++;
    }

    if (count == 0) {
        printf("No students found matching the search criteria.\n");
        printf("لم يتم العثور على طلاب مطابقين لمعايير البحث.\n");
    } else {
        printf("================================================================\n");
        printf("Found %d student(s).\n", count);
        printf("تم العثور على %d طالب/طلاب.\n", count);
    }

    if (rc != SQLITE_DONE) {
        fprintf(stderr, "Error reading data: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        return 1;
    }

    sqlite3_finalize(stmt);
    return 0;
}

/*
 * Update student information
 * تحديث معلومات الطالب
 */
int update_student(void) {
    printf("\n=== Update Student / تحديث بيانات الطالب ===\n");

    int student_id = get_integer_input("Enter student ID to update / أدخل الرقم الجامعي للطالب: ", 1, 999999);

    // First, check if student exists
    const char *check_sql = "SELECT name, age, department, gpa FROM students WHERE student_id = ?";
    sqlite3_stmt *check_stmt;

    int rc = sqlite3_prepare_v2(db, check_sql, -1, &check_stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    sqlite3_bind_int(check_stmt, 1, student_id);

    rc = sqlite3_step(check_stmt);
    if (rc != SQLITE_ROW) {
        printf("Student with ID %d not found.\n", student_id);
        printf("لم يتم العثور على طالب بالرقم الجامعي %d.\n", student_id);
        sqlite3_finalize(check_stmt);
        return 1;
    }

    // Display current information
    printf("\nCurrent student information / المعلومات الحالية للطالب:\n");
    printf("Name / الاسم: %s\n", sqlite3_column_text(check_stmt, 0));
    printf("Age / العمر: %d\n", sqlite3_column_int(check_stmt, 1));
    printf("Department / القسم: %s\n", sqlite3_column_text(check_stmt, 2));
    printf("GPA / المعدل: %.2f\n", sqlite3_column_double(check_stmt, 3));

    sqlite3_finalize(check_stmt);

    // Get new information
    printf("\nEnter new information (press Enter to keep current value):\n");
    printf("أدخل المعلومات الجديدة (اضغط Enter للاحتفاظ بالقيمة الحالية):\n");

    Student student;
    student.student_id = student_id;

    get_string_input("New name / الاسم الجديد: ", student.name, MAX_NAME_LENGTH);
    student.age = get_integer_input("New age / العمر الجديد: ", 15, 100);
    get_string_input("New department / القسم الجديد: ", student.department, MAX_DEPT_LENGTH);
    student.gpa = get_float_input("New GPA / المعدل الجديد: ", 0.0, 4.0);

    // Update the student
    const char *update_sql = "UPDATE students SET name = ?, age = ?, department = ?, gpa = ? WHERE student_id = ?";
    sqlite3_stmt *update_stmt;

    rc = sqlite3_prepare_v2(db, update_sql, -1, &update_stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    sqlite3_bind_text(update_stmt, 1, student.name, -1, SQLITE_STATIC);
    sqlite3_bind_int(update_stmt, 2, student.age);
    sqlite3_bind_text(update_stmt, 3, student.department, -1, SQLITE_STATIC);
    sqlite3_bind_double(update_stmt, 4, student.gpa);
    sqlite3_bind_int(update_stmt, 5, student_id);

    rc = sqlite3_step(update_stmt);
    if (rc == SQLITE_DONE) {
        printf("Student information updated successfully!\n");
        printf("تم تحديث معلومات الطالب بنجاح!\n");
    } else {
        fprintf(stderr, "Failed to update student: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(update_stmt);
        return 1;
    }

    sqlite3_finalize(update_stmt);
    return 0;
}

/*
 * Delete a student from the database
 * حذف طالب من قاعدة البيانات
 */
int delete_student(void) {
    printf("\n=== Delete Student / حذف طالب ===\n");

    int student_id = get_integer_input("Enter student ID to delete / أدخل الرقم الجامعي للطالب: ", 1, 999999);

    // First, check if student exists and show information
    const char *check_sql = "SELECT name, age, department, gpa FROM students WHERE student_id = ?";
    sqlite3_stmt *check_stmt;

    int rc = sqlite3_prepare_v2(db, check_sql, -1, &check_stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    sqlite3_bind_int(check_stmt, 1, student_id);

    rc = sqlite3_step(check_stmt);
    if (rc != SQLITE_ROW) {
        printf("Student with ID %d not found.\n", student_id);
        printf("لم يتم العثور على طالب بالرقم الجامعي %d.\n", student_id);
        sqlite3_finalize(check_stmt);
        return 1;
    }

    // Display student information
    printf("\nStudent to be deleted / الطالب المراد حذفه:\n");
    printf("ID / الرقم: %d\n", student_id);
    printf("Name / الاسم: %s\n", sqlite3_column_text(check_stmt, 0));
    printf("Age / العمر: %d\n", sqlite3_column_int(check_stmt, 1));
    printf("Department / القسم: %s\n", sqlite3_column_text(check_stmt, 2));
    printf("GPA / المعدل: %.2f\n", sqlite3_column_double(check_stmt, 3));

    sqlite3_finalize(check_stmt);

    // Confirm deletion
    printf("\nAre you sure you want to delete this student? (y/n): ");
    printf("هل أنت متأكد من حذف هذا الطالب؟ (y/n): ");

    char confirm;
    scanf(" %c", &confirm);
    clear_input_buffer();

    if (confirm != 'y' && confirm != 'Y') {
        printf("Deletion cancelled.\n");
        printf("تم إلغاء عملية الحذف.\n");
        return 0;
    }

    // Delete the student
    const char *delete_sql = "DELETE FROM students WHERE student_id = ?";
    sqlite3_stmt *delete_stmt;

    rc = sqlite3_prepare_v2(db, delete_sql, -1, &delete_stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    sqlite3_bind_int(delete_stmt, 1, student_id);

    rc = sqlite3_step(delete_stmt);
    if (rc == SQLITE_DONE) {
        printf("Student deleted successfully!\n");
        printf("تم حذف الطالب بنجاح!\n");
    } else {
        fprintf(stderr, "Failed to delete student: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(delete_stmt);
        return 1;
    }

    sqlite3_finalize(delete_stmt);
    return 0;
}

/*
 * Get total number of students
 * الحصول على العدد الإجمالي للطلاب
 */
int get_student_count(void) {
    const char *sql = "SELECT COUNT(*) FROM students";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return -1;
    }

    int count = 0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }

    sqlite3_finalize(stmt);
    return count;
}

/*
 * Get average age of all students
 * الحصول على متوسط أعمار الطلاب
 */
float get_average_age(void) {
    const char *sql = "SELECT AVG(age) FROM students";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return -1.0;
    }

    float avg_age = 0.0;
    if (sqlite3_step(stmt) == SQLITE_ROW) {
        avg_age = (float)sqlite3_column_double(stmt, 0);
    }

    sqlite3_finalize(stmt);
    return avg_age;
}

/*
 * Display system statistics
 * عرض إحصائيات النظام
 */
void display_statistics(void) {
    printf("\n=== System Statistics / إحصائيات النظام ===\n");

    int total_students = get_student_count();
    if (total_students < 0) {
        printf("Error retrieving student count.\n");
        printf("خطأ في استرداد عدد الطلاب.\n");
        return;
    }

    printf("Total Students / إجمالي الطلاب: %d\n", total_students);

    if (total_students > 0) {
        float avg_age = get_average_age();
        if (avg_age >= 0) {
            printf("Average Age / متوسط العمر: %.1f years\n", avg_age);
        }

        // Get GPA statistics
        const char *gpa_sql = "SELECT AVG(gpa), MIN(gpa), MAX(gpa) FROM students";
        sqlite3_stmt *stmt;

        int rc = sqlite3_prepare_v2(db, gpa_sql, -1, &stmt, NULL);
        if (rc == SQLITE_OK && sqlite3_step(stmt) == SQLITE_ROW) {
            printf("Average GPA / متوسط المعدل التراكمي: %.2f\n", sqlite3_column_double(stmt, 0));
            printf("Minimum GPA / أقل معدل تراكمي: %.2f\n", sqlite3_column_double(stmt, 1));
            printf("Maximum GPA / أعلى معدل تراكمي: %.2f\n", sqlite3_column_double(stmt, 2));
        }
        sqlite3_finalize(stmt);

        // Department statistics
        printf("\nDepartment Statistics / إحصائيات الأقسام:\n");
        const char *dept_sql = "SELECT department, COUNT(*) FROM students GROUP BY department ORDER BY COUNT(*) DESC";

        rc = sqlite3_prepare_v2(db, dept_sql, -1, &stmt, NULL);
        if (rc == SQLITE_OK) {
            printf("%-20s %s\n", "Department", "Count");
            printf("%-20s %s\n", "القسم", "العدد");
            printf("--------------------------------\n");

            while (sqlite3_step(stmt) == SQLITE_ROW) {
                printf("%-20s %d\n",
                       sqlite3_column_text(stmt, 0),
                       sqlite3_column_int(stmt, 1));
            }
        }
        sqlite3_finalize(stmt);
    } else {
        printf("No students in the database.\n");
        printf("لا توجد بيانات طلاب في قاعدة البيانات.\n");
    }
}

/*
 * Backup database to a file
 * نسخ احتياطي لقاعدة البيانات
 */
int backup_database(void) {
    printf("\n=== Backup Database / نسخ احتياطي للبيانات ===\n");

    char filename[MAX_FILENAME_LENGTH];
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);

    strftime(filename, sizeof(filename), "backup_students_%Y%m%d_%H%M%S.db", tm_info);

    printf("Creating backup: %s\n", filename);
    printf("جاري إنشاء نسخة احتياطية: %s\n", filename);

    sqlite3 *backup_db;
    int rc = sqlite3_open(filename, &backup_db);

    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot create backup file: %s\n", sqlite3_errmsg(backup_db));
        return 1;
    }

    sqlite3_backup *backup = sqlite3_backup_init(backup_db, "main", db, "main");
    if (backup == NULL) {
        fprintf(stderr, "Cannot initialize backup: %s\n", sqlite3_errmsg(backup_db));
        sqlite3_close(backup_db);
        return 1;
    }

    rc = sqlite3_backup_step(backup, -1);
    if (rc == SQLITE_DONE) {
        printf("Backup completed successfully!\n");
        printf("تم إنشاء النسخة الاحتياطية بنجاح!\n");
    } else {
        fprintf(stderr, "Backup failed: %s\n", sqlite3_errmsg(backup_db));
    }

    sqlite3_backup_finish(backup);
    sqlite3_close(backup_db);

    return (rc == SQLITE_DONE) ? 0 : 1;
}

/*
 * Restore database from backup file
 * استعادة قاعدة البيانات من ملف النسخة الاحتياطية
 */
int restore_database(void) {
    printf("\n=== Restore Database / استعادة قاعدة البيانات ===\n");

    char filename[MAX_FILENAME_LENGTH];
    get_string_input("Enter backup filename / أدخل اسم ملف النسخة الاحتياطية: ", filename, MAX_FILENAME_LENGTH);

    // Check if backup file exists
    FILE *file = fopen(filename, "r");
    if (file == NULL) {
        printf("Backup file not found: %s\n", filename);
        printf("ملف النسخة الاحتياطية غير موجود: %s\n", filename);
        return 1;
    }
    fclose(file);

    printf("Warning: This will replace all current data!\n");
    printf("تحذير: هذا سيستبدل جميع البيانات الحالية!\n");
    printf("Continue? (y/n): ");

    char confirm;
    scanf(" %c", &confirm);
    clear_input_buffer();

    if (confirm != 'y' && confirm != 'Y') {
        printf("Restore cancelled.\n");
        printf("تم إلغاء عملية الاستعادة.\n");
        return 0;
    }

    sqlite3 *backup_db;
    int rc = sqlite3_open(filename, &backup_db);

    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot open backup file: %s\n", sqlite3_errmsg(backup_db));
        return 1;
    }

    sqlite3_backup *backup = sqlite3_backup_init(db, "main", backup_db, "main");
    if (backup == NULL) {
        fprintf(stderr, "Cannot initialize restore: %s\n", sqlite3_errmsg(db));
        sqlite3_close(backup_db);
        return 1;
    }

    rc = sqlite3_backup_step(backup, -1);
    if (rc == SQLITE_DONE) {
        printf("Database restored successfully!\n");
        printf("تم استعادة قاعدة البيانات بنجاح!\n");
    } else {
        fprintf(stderr, "Restore failed: %s\n", sqlite3_errmsg(db));
    }

    sqlite3_backup_finish(backup);
    sqlite3_close(backup_db);

    return (rc == SQLITE_DONE) ? 0 : 1;
}

/*
 * Generate test data (200 students)
 * إنشاء بيانات اختبار (200 طالب)
 */
int generate_test_data(void) {
    printf("\n=== Generate Test Data / إنشاء بيانات اختبار ===\n");

    printf("This will add 200 test students to the database.\n");
    printf("سيتم إضافة 200 طالب اختبار إلى قاعدة البيانات.\n");
    printf("Continue? (y/n): ");

    char confirm;
    scanf(" %c", &confirm);
    clear_input_buffer();

    if (confirm != 'y' && confirm != 'Y') {
        printf("Test data generation cancelled.\n");
        printf("تم إلغاء إنشاء بيانات الاختبار.\n");
        return 0;
    }

    const char *first_names[] = {"Ahmed", "Fatima", "Mohammed", "Aisha", "Omar", "Khadija", "Ali", "Zainab", "Hassan", "Maryam",
                                "Ibrahim", "Nour", "Youssef", "Layla", "Khalid", "Amina", "Saad", "Huda", "Tariq", "Salma"};
    const char *last_names[] = {"Al-Ahmad", "Al-Mohammed", "Al-Hassan", "Al-Ali", "Al-Omar", "Al-Khalid", "Al-Saad", "Al-Tariq",
                               "Al-Ibrahim", "Al-Youssef", "Al-Nasser", "Al-Mansour", "Al-Rashid", "Al-Fahad", "Al-Sultan"};
    const char *departments[] = {"Computer Science", "Engineering", "Medicine", "Business", "Mathematics", "Physics", "Chemistry", "Biology"};

    int first_names_count = sizeof(first_names) / sizeof(first_names[0]);
    int last_names_count = sizeof(last_names) / sizeof(last_names[0]);
    int departments_count = sizeof(departments) / sizeof(departments[0]);

    srand((unsigned int)time(NULL));

    const char *sql = "INSERT INTO students (name, age, department, gpa, enrollment_date) VALUES (?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;

    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    printf("Generating test data...\n");
    printf("جاري إنشاء بيانات الاختبار...\n");

    for (int i = 0; i < 200; i++) {
        char full_name[MAX_NAME_LENGTH];
        snprintf(full_name, sizeof(full_name), "%s %s",
                first_names[rand() % first_names_count],
                last_names[rand() % last_names_count]);

        int age = 18 + (rand() % 8); // Age between 18-25
        const char *department = departments[rand() % departments_count];
        float gpa = 2.0 + ((float)rand() / RAND_MAX) * 2.0; // GPA between 2.0-4.0

        // Random enrollment date within last 4 years
        time_t now = time(NULL);
        time_t enrollment_time = now - (rand() % (4 * 365 * 24 * 3600));
        struct tm *tm_info = localtime(&enrollment_time);
        char enrollment_date[20];
        strftime(enrollment_date, sizeof(enrollment_date), "%Y-%m-%d", tm_info);

        sqlite3_bind_text(stmt, 1, full_name, -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 2, age);
        sqlite3_bind_text(stmt, 3, department, -1, SQLITE_STATIC);
        sqlite3_bind_double(stmt, 4, gpa);
        sqlite3_bind_text(stmt, 5, enrollment_date, -1, SQLITE_STATIC);

        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            fprintf(stderr, "Failed to insert test data: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            return 1;
        }

        sqlite3_reset(stmt);

        if ((i + 1) % 50 == 0) {
            printf("Generated %d students...\n", i + 1);
            printf("تم إنشاء %d طالب...\n", i + 1);
        }
    }

    sqlite3_finalize(stmt);

    printf("Test data generation completed! 200 students added.\n");
    printf("تم إنشاء بيانات الاختبار بنجاح! تم إضافة 200 طالب.\n");

    return 0;
}

/*
 * Delete all students from database
 * حذف جميع الطلاب من قاعدة البيانات
 */
int delete_all_students(void) {
    printf("\n=== Delete All Students / حذف جميع الطلاب ===\n");

    int count = get_student_count();
    if (count <= 0) {
        printf("No students to delete.\n");
        printf("لا توجد بيانات طلاب للحذف.\n");
        return 0;
    }

    printf("WARNING: This will delete ALL %d students from the database!\n", count);
    printf("تحذير: سيتم حذف جميع الطلاب البالغ عددهم %d من قاعدة البيانات!\n", count);
    printf("This action cannot be undone!\n");
    printf("لا يمكن التراجع عن هذا الإجراء!\n");
    printf("Are you absolutely sure? (type 'DELETE' to confirm): ");
    printf("هل أنت متأكد تماماً؟ (اكتب 'DELETE' للتأكيد): ");

    char confirmation[20];
    fgets(confirmation, sizeof(confirmation), stdin);

    // Remove newline character
    confirmation[strcspn(confirmation, "\n")] = 0;

    if (strcmp(confirmation, "DELETE") != 0) {
        printf("Deletion cancelled.\n");
        printf("تم إلغاء عملية الحذف.\n");
        return 0;
    }

    const char *sql = "DELETE FROM students";
    char *err_msg = 0;

    int rc = sqlite3_exec(db, sql, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to delete students: %s\n", err_msg);
        sqlite3_free(err_msg);
        return 1;
    }

    // Reset auto-increment counter
    const char *reset_sql = "DELETE FROM sqlite_sequence WHERE name='students'";
    sqlite3_exec(db, reset_sql, 0, 0, 0);

    printf("All students deleted successfully!\n");
    printf("تم حذف جميع الطلاب بنجاح!\n");

    return 0;
}

/*
 * Get integer input with validation
 * الحصول على مدخل رقم صحيح مع التحقق
 */
int get_integer_input(const char* prompt, int min, int max) {
    int value;
    char buffer[100];

    while (1) {
        printf("%s", prompt);

        if (fgets(buffer, sizeof(buffer), stdin) == NULL) {
            printf("Error reading input.\n");
            continue;
        }

        // Check if input is empty (just Enter pressed)
        if (buffer[0] == '\n') {
            continue;
        }

        char *endptr;
        value = (int)strtol(buffer, &endptr, 10);

        // Check if conversion was successful and entire string was consumed
        if (endptr == buffer || *endptr != '\n') {
            printf("Invalid input. Please enter a number.\n");
            printf("مدخل غير صحيح. يرجى إدخال رقم.\n");
            continue;
        }

        if (value < min || value > max) {
            printf("Value must be between %d and %d.\n", min, max);
            printf("القيمة يجب أن تكون بين %d و %d.\n", min, max);
            continue;
        }

        break;
    }

    return value;
}

/*
 * Get float input with validation
 * الحصول على مدخل رقم عشري مع التحقق
 */
float get_float_input(const char* prompt, float min, float max) {
    float value;
    char buffer[100];

    while (1) {
        printf("%s", prompt);

        if (fgets(buffer, sizeof(buffer), stdin) == NULL) {
            printf("Error reading input.\n");
            continue;
        }

        // Check if input is empty (just Enter pressed)
        if (buffer[0] == '\n') {
            continue;
        }

        char *endptr;
        value = strtof(buffer, &endptr);

        // Check if conversion was successful and entire string was consumed
        if (endptr == buffer || *endptr != '\n') {
            printf("Invalid input. Please enter a number.\n");
            printf("مدخل غير صحيح. يرجى إدخال رقم.\n");
            continue;
        }

        if (value < min || value > max) {
            printf("Value must be between %.2f and %.2f.\n", min, max);
            printf("القيمة يجب أن تكون بين %.2f و %.2f.\n", min, max);
            continue;
        }

        break;
    }

    return value;
}

/*
 * Get string input with validation
 * الحصول على مدخل نصي مع التحقق
 */
void get_string_input(const char* prompt, char* buffer, int max_length) {
    while (1) {
        printf("%s", prompt);

        if (fgets(buffer, max_length, stdin) == NULL) {
            printf("Error reading input.\n");
            continue;
        }

        // Remove newline character
        buffer[strcspn(buffer, "\n")] = 0;

        // Check if input is empty
        if (strlen(buffer) == 0) {
            printf("Input cannot be empty.\n");
            printf("المدخل لا يمكن أن يكون فارغاً.\n");
            continue;
        }

        // Check for minimum length (at least 2 characters for names)
        if (strlen(buffer) < 2) {
            printf("Input too short. Please enter at least 2 characters.\n");
            printf("المدخل قصير جداً. يرجى إدخال حرفين على الأقل.\n");
            continue;
        }

        break;
    }
}

/*
 * Clear input buffer
 * مسح مخزن المدخلات
 */
void clear_input_buffer(void) {
    int c;
    while ((c = getchar()) != '\n' && c != EOF);
}
